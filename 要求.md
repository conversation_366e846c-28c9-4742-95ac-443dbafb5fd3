# 自动咖啡机售卖数据分析任务要求

## 数据集说明

本项目包含两个CSV数据文件：

1. **index_1.csv** - 2024年3月至2025年3月的咖啡销售数据（主要数据表）

   - 日期：销售日期
   - 日期时间：具体销售时间
   - 现金类型：支付方式（card/cash）
   - 银行卡：银行卡编号（仅card支付有此字段）
   - 金额：销售金额
   - 咖啡名称：咖啡产品名称
2. **index_2.csv** - 2025年2月至2025年3月的咖啡销售数据（补充数据表）

   - 日期：销售日期
   - 日期时间：具体销售时间
   - 现金类型：支付方式（card/cash）
   - 金额：销售金额
   - 咖啡名称：咖啡产品名称

## 技术要求

1. **编程语言**：使用Python进行数据分析
2. **代码组织**：每个问题对应一个独立的Python文件，文件命名格式为 `question_XX.py`
3. **数据处理**：使用pandas库进行CSV文件读取和处理
4. **输出格式**：所有结果必须以Markdown格式的DataFrame输出，使用 `df.to_markdown()` 方法
5. **数据关联**：必须使用两个数据表进行JOIN操作分析
6. **时间处理**：正确处理日期时间字段进行时间范围筛选

## 分析问题

**注意：以下问题必须通过合并两个数据表才能完成分析**

| 问题编号 | 分析问题                                                                                                                                              |
| -------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1        | 在2025年2月期间，两个数据表中都有销售记录的咖啡产品有哪些？请列出产品名称、在index_1表中的销售总额、在index_2表中的销售总额，以及两表销售总额的差值。 |
| 2        | 对比2025年2月期间，index_1表和index_2表中相同咖啡产品的平均单价差异。哪个产品的平均单价差异最大？具体差异金额是多少？                                 |
| 3        | 在2025年2月至2025年3月期间，合并两个数据表后，哪种支付方式（card/cash）的总销售额最高？具体金额是多少？该支付方式占总销售额的百分比是多少？           |

## 输出要求

1. 每个Python文件应包含完整的数据处理流程，包括两表的读取、合并、筛选和分析
2. 结果必须使用 `print(df.to_markdown(index=False))` 输出为Markdown格式
3. 对于对比类问题，输出应包含详细的对比数据和计算过程
4. 代码应包含适当的注释说明JOIN操作和处理逻辑
5. 输出结果需要保存到文档中
6. 必须验证JOIN操作的正确性，确保数据关联准确
